import { chat, chatStream, generateImage, getProviders, getModels } from './app.js';

/**
 * Example usage of the AI App functions
 */

// Example 1: Simple chat (non-streaming)
async function simpleChat() {
    console.log('=== Simple Chat Example === 1 ');

    try {
        const response = await chat(
            'pollinations',           // provider
            'gpt-4.1',               // model
            'Hello! How are you?'    // prompt
        );

        console.log('Response:', response.choices[0].message.content);
    } catch (error) {
        console.error('Chat error:', error.message);
    }
}

// Example 2: Streaming chat
async function streamingChat() {
    console.log('\n=== Streaming Chat Example === 2 ');
    
    let fullResponse = '';
    
    try {
        await chatStream(
            'pollinations',                    // provider
            'gpt-4.1',                        // model
            'Write a short poem about AI',    // prompt
            (chunk) => {                      // onChunk callback
                if (chunk.choices?.[0]?.delta?.content) {
                    const content = chunk.choices[0].delta.content;
                    process.stdout.write(content); // Stream to console
                    fullResponse += content;
                }
            },
            { temperature: 0.7 }              // options
        );
        
        console.log('\n\nFull response received:', fullResponse.length, 'characters');
    } catch (error) {
        console.error('\n❌ Streaming error:', error.message);
    }
}

// Example 3: Chat with conversation history
async function conversationChat() {
    console.log('\n=== Conversation Chat Example === 3');
    
    const messages = [
        { role: 'user', content: 'What is the capital of France?' },
        { role: 'assistant', content: 'The capital of France is Paris.' },
        { role: 'user', content: 'What is its population?' }
    ];
    
    try {
        const response = await chat(
            'pollinations',
            'gpt-4.1',
            messages , // Pass messages array instead of string
             (chunk) => {                      // onChunk callback
                if (chunk.choices?.[0]?.delta?.content) {
                    const content = chunk.choices[0].delta.content;
                    process.stdout.write(content); // Stream to console
                    fullResponse += content;
                }
            },

        );
        
        console.log('Response:', response.choices[0].message.content);
    } catch (error) {
        console.error('Conversation error:', error);
    }
}

// Example 4: Streaming chat with conversation history
async function streamingConversationChat() {
    console.log('\n=== Streaming Conversation Chat Example === 4');

    const messages = [
        { role: 'user', content: 'What is the capital of France?' },
        { role: 'assistant', content: 'The capital of France is Paris.' },
        { role: 'user', content: 'What is its population and tell me about its history?' }
    ];

    let fullResponse = '';

    try {
        console.log('Conversation history:');
        messages.forEach((msg, i) => {
            console.log(`${i + 1}. ${msg.role}: ${msg.content}`);
        });
        console.log('\nStreaming response:');

        await chatStream(
            'pollinations',                    // provider
            'gpt-4.1',                        // model
            messages,                         // conversation history
            (chunk) => {                      // onChunk callback
                if (chunk.choices?.[0]?.delta?.content) {
                    const content = chunk.choices[0].delta.content;
                    process.stdout.write(content); // Stream to console
                    fullResponse += content;
                }
            },
            { temperature: 0.7 }              // options
        );

        console.log('\n\nFull response received:', fullResponse.length, 'characters');
    } catch (error) {
        console.error('\n❌ Streaming conversation error:', error.message);
    }
}

// Example 5: Image generation
async function imageGeneration() {
    console.log('\n=== Image Generation Example === 5');
    
    try {
        const response = await generateImage(
            'pollinations',                           // provider
            'flux',                                   // model
            'A beautiful sunset over mountains',      // prompt
            { 
                size: '1024x1024',                   // options
                quality: 'standard'
            }
        );
        
        console.log('Image URL:', response.data[0].url);
    } catch (error) {
        console.error('Image generation error:', error);
    }
}

// Example 5: Get available providers and models
async function listProvidersAndModels() {
    console.log('\n=== Available Providers and Models === 6');
    
    const providers = getProviders();
    console.log('Available providers:', providers);
    
    for (const provider of providers) {
        try {
            console.log(`\n${provider.toUpperCase()} models:`);
            const models = await getModels(provider);
            
            // Show first 5 models for each provider
            const chatModels = models.filter(m => m.type === 'chat').slice(0, 5);
            const imageModels = models.filter(m => m.type === 'image').slice(0, 3);
            
            if (chatModels.length > 0) {
                console.log('  Chat models:', chatModels.map(m => m.id).join(', '));
            }
            if (imageModels.length > 0) {
                console.log('  Image models:', imageModels.map(m => m.id).join(', '));
            }
        } catch (error) {
            console.log(`  ❌ Error getting models: ${error.message}`);
        }
    }
}

// Example 6: Advanced streaming with custom handling
async function advancedStreaming() {
    console.log('\n=== Advanced Streaming Example === 7');
    
    let wordCount = 0;
    let startTime = Date.now();
    
    try {
        await chatStream(
            'pollinations',
            'gpt-4.1',
            'Explain quantum computing in simple terms',
            (chunk) => {
                if (chunk.choices?.[0]?.delta?.content) {
                    const content = chunk.choices[0].delta.content;
                    
                    // Count words
                    wordCount += content.split(/\s+/).filter(word => word.length > 0).length;
                    
                    // Show progress
                    if (wordCount % 10 === 0) {
                        const elapsed = (Date.now() - startTime) / 1000;
                        console.log(`\n[Progress: ${wordCount} words in ${elapsed.toFixed(1)}s]`);
                    }
                    
                    process.stdout.write(content);
                }
                
                // Handle other chunk types
                if (chunk.choices?.[0]?.finish_reason) {
                    console.log(`\n[Finished: ${chunk.choices[0].finish_reason}]`);
                }
            },
            {
                max_tokens: 200,
                temperature: 0.5
            }
        );
    } catch (error) {
        console.error('Advanced streaming error:', error);
    }
}

// Example 7: Error handling and fallback
async function errorHandlingExample() {
    console.log('\n=== Error Handling Example === 8');

    const providers = ['pollinations', 'deepinfra'];
    const prompt = 'What is machine learning?';

    for (const provider of providers) {
        try {
            console.log(`Trying ${provider}...`);
            const response = await chat(provider, 'gpt-4.1', prompt);
            console.log(`Success with ${provider}:`, response.choices[0].message.content.substring(0, 100) + '...');
            break; // Stop on first success
        } catch (error) {
            console.log(`${provider} failed:`, error.message);
            // Continue to next provider
        }
    }
}

// Run all examples
async function runExamples() {
    console.log('🤖 AI App Examples\n');

    // Check if we have any providers available
    const providers = getProviders();
    if (providers.length === 0) {
        console.error('❌ No AI providers available. Cannot run examples.');
        return;
    }

    console.log('Running examples with available providers...\n');

    try {
        await listProvidersAndModels();
        await simpleChat();
        await conversationChat();
        await streamingChat();
        await streamingConversationChat();
        await advancedStreaming();

        // Only try image generation if we have providers that support it
        if (providers.includes('pollinations')) {
            await imageGeneration();
        } else {
            console.log('\n=== Image Generation Example ===');
            console.log('⚠️  Skipped: No image generation providers available');
        }

        await errorHandlingExample();

        console.log('\n✅ All examples completed!');
    } catch (error) {
        console.error('\n❌ Error running examples:', error.message);
    }
}

// Export functions for individual testing
export {
    simpleChat,
    streamingChat,
    conversationChat,
    streamingConversationChat,
    imageGeneration,
    listProvidersAndModels,
    advancedStreaming,
    errorHandlingExample,
    runExamples
};

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runExamples().catch(console.error);
}
